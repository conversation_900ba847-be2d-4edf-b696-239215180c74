{"version": "2.0.0", "tasks": [{"label": "Maven: <PERSON> Main", "type": "shell", "command": "mvn", "args": ["exec:java"], "group": {"kind": "build", "isDefault": true}, "options": {"cwd": "${workspaceFolder}/demo", "shell": {"executable": "powershell.exe", "args": ["-Command"]}}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Maven: Clean Compile", "type": "shell", "command": "mvn", "args": ["clean", "compile"], "group": "build", "options": {"cwd": "${workspaceFolder}/demo", "shell": {"executable": "powershell.exe", "args": ["-Command"]}}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Maven: Quick Run (Skip Tests)", "type": "shell", "command": "mvn", "args": ["exec:java", "-DskipTests"], "group": "build", "options": {"cwd": "${workspaceFolder}/demo", "shell": {"executable": "powershell.exe", "args": ["-Command"]}}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}