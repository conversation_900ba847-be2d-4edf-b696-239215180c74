package com.ibm;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.files.FileCreateParams;
import com.openai.models.files.FileObject;
import com.openai.models.files.FilePurpose;

import java.io.PrintStream;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Main class for the application.
 *
 * <AUTHOR> Name]
 */
public class Main {
    public static void main(String[] args) {
        try {
            System.setOut(new PrintStream(System.out, true, "UTF-8"));
            // 创建客户端，使用环境变量中的API密钥
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey("sk-64e26a3cc8e644d49987870a2cdcf4b8")
                    .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                    .build();
            // 设置文件路径,请根据实际需求修改路径与文件?
            Path filePath = Paths.get("src/main/resources/测试文本.xlsx");
            // 创建文件上传参数
            FileCreateParams fileParams = FileCreateParams.builder()
                    .file(filePath)
                    .purpose(FilePurpose.of("file-extract"))
                    .build();

            // 上传文件打印fileid
            FileObject fileObject = client.files().create(fileParams);
            System.out.println(fileObject.id());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}