{"version": "2.0.0", "tasks": [{"label": "Maven: <PERSON> Main", "type": "shell", "command": "mvn", "args": ["compile", "exec:java", "-Dexec.mainClass=com.ibm.Main"], "group": {"kind": "build", "isDefault": true}, "options": {"cwd": "${workspaceFolder}/demo"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Maven: Clean Compile", "type": "shell", "command": "mvn", "args": ["clean", "compile"], "group": "build", "options": {"cwd": "${workspaceFolder}/demo"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}