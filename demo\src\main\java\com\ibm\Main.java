package com.ibm;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.files.FileCreateParams;
import com.openai.models.files.FileObject;
import com.openai.models.files.FilePurpose;

import java.io.PrintStream;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Main class for the application.
 *
 * <AUTHOR> Name]
 */
public class Main {
    public static void main(String[] args) {
        try {
            System.setOut(new PrintStream(System.out, true, "UTF-8"));
            // 创建客户端，使用环境变量中的API密钥
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey("sk-64e26a3cc8e644d49987870a2cdcf4b8")
                    .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                    .build();
            // 设置文件路径,请根据实际需求修改路径与文件?
            // 尝试多个可能的路径位置
            Path filePath = null;
            String[] possiblePaths = {
                    "src/main/resources/测试文本.xlsx", // Maven运行时的路径
                    "demo/src/main/resources/测试文本.xlsx" // 从项目根目录运行时的路径
            };

            for (String pathStr : possiblePaths) {
                Path testPath = Paths.get(pathStr);
                if (testPath.toFile().exists()) {
                    filePath = testPath;
                    break;
                }
            }

            if (filePath == null) {
                System.err.println("找不到测试文件，请检查文件路径");
                return;
            }

            System.out.println("使用文件路径: " + filePath.toAbsolutePath());
            // 创建文件上传参数
            FileCreateParams fileParams = FileCreateParams.builder()
                    .file(filePath)
                    .purpose(FilePurpose.of("file-extract"))
                    .build();

            // 上传文件打印fileid
            FileObject fileObject = client.files().create(fileParams);
            System.out.println(fileObject.id());

            // 程序结束前稍等片刻，让后台线程自然结束
            Thread.sleep(1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}